import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voji/utils/logger.dart';

/// Key for storing the group by color preference
const String _groupByColorKey = 'group_by_color';

/// Provider to track whether ideabooks should be grouped by color
final groupByColorProvider = StateNotifierProvider<GroupByColorNotifier, bool>((ref) {
  return GroupByColorNotifier();
});

/// Notifier for managing group by color state with persistence
class GroupByColorNotifier extends StateNotifier<bool> {
  /// Constructor
  GroupByColorNotifier() : super(true) {
    // Load saved preference when initialized
    _loadSavedPreference();
  }

  /// Toggle between grouping by color and no grouping
  void toggleGroupByColor() {
    final oldState = state;
    final newState = !oldState;
    Logger.debug('Group by color toggling from $oldState to $newState');
    state = newState;
    // Save the new preference
    _savePreference(newState);
    Logger.debug('Group by color toggled to: $newState');
  }

  /// Set group by color explicitly
  void setGroupByColor(bool value) {
    state = value;
    // Save the new preference
    _savePreference(value);
    Logger.debug('Group by color set to: $value');
  }

  /// Load the saved preference from storage
  Future<void> _loadSavedPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedValue = prefs.getBool(_groupByColorKey);
      if (savedValue != null) {
        state = savedValue;
        Logger.debug('Loaded group by color preference: $savedValue');
      } else {
        // No saved preference, use default value (true - grouped by color)
        state = true;
        Logger.debug('No group by color preference found, using default: true');
      }
    } catch (e) {
      Logger.error('Error loading group by color preference: $e');
      // On error, use default value (true - grouped by color)
      state = true;
    }
  }

  /// Save the preference to storage
  Future<void> _savePreference(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_groupByColorKey, value);
      Logger.debug('Saved group by color preference: $value');
    } catch (e) {
      Logger.error('Error saving group by color preference: $e');
    }
  }
}

/// Provider to log group by color changes
final groupByColorLoggerProvider = Provider<void>((ref) {
  ref.listen(groupByColorProvider, (previous, next) {
    Logger.debug('Group by color changed from $previous to $next');
  });
});
