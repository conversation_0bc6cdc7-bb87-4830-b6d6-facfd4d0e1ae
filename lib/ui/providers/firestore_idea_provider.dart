import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/services/firebase/firestore_service.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';

/// Notifier for managing ideas with Firestore real-time updates
class FirestoreIdeasNotifier extends StateNotifier<AsyncValue<List<Idea>>> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Ideabook ID
  final String _ideabookId;

  /// Getter for ideabook ID
  String get ideabookId => _ideabookId;

  /// Subscription to Firestore updates
  StreamSubscription<List<Idea>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Constructor
  FirestoreIdeasNotifier(this._firestoreService, this._ideabookId) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Flag to track if we should skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering ideas
  bool _skipNextUpdate = false;

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore ideas updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Starting to listen to Firestore ideas updates for ideabook $_ideabookId');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Listen to Firestore updates
      _subscription = _firestoreService.listenToIdeas(_ideabookId).listen(
        (ideas) {
          Logger.debug('Received ${ideas.length} ideas from Firestore for ideabook $_ideabookId');

          // If we should skip this update, log it and reset the flag
          if (_skipNextUpdate) {
            Logger.debug('Skipping UI update for ideas in ideabook $_ideabookId (optimistic update already applied)');
            _skipNextUpdate = false;
            return;
          }

          state = AsyncValue.data(ideas);
        },
        onError: (error, stackTrace) {
          Logger.error('Error listening to Firestore ideas for ideabook $_ideabookId', error);
          state = AsyncValue.error(error, stackTrace);
        },
      );

      // Register the subscription with the global registry for comprehensive cleanup
      if (_subscription != null) {
        final globalRegistry = GlobalFirestoreSubscriptionRegistry();
        globalRegistry.register(_subscription!);
      }
    } catch (e, stack) {
      Logger.error('Error setting up Firestore ideas listener for ideabook $_ideabookId', e);
      state = AsyncValue.error(e, stack);
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore ideas updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Stopping Firestore ideas listener for ideabook $_ideabookId');

    // Unregister from the global registry
    if (_subscription != null) {
      final globalRegistry = GlobalFirestoreSubscriptionRegistry();
      globalRegistry.unregister(_subscription!);
    }

    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  /// Create a new idea
  Future<Idea> createIdea({
    required String content,
    Function? onCreated,
  }) async {
    Logger.debug('Creating idea in Firestore for ideabook $_ideabookId');

    try {
      // Create the idea model with current time as placeholder
      // The actual creation timestamp will be set by Firestore's serverTimestamp()
      final now = DateTime.now();
      final idea = Idea(
        id: IdUtils.generateId(), // This ID will be replaced by Firestore
        content: content,
        createdAt: now, // This will be replaced by server timestamp
        updatedAt: now,
      );

      // Create the idea in Firestore
      final newIdea = await _firestoreService.createIdea(_ideabookId, idea);
      Logger.debug('Created idea in Firestore: ${newIdea.id}');

      // Call the callback if provided
      if (onCreated != null) {
        onCreated();
      }

      return newIdea;
    } catch (e) {
      Logger.error('Error creating idea in Firestore', e);
      rethrow;
    }
  }

  /// Update an idea
  Future<bool> updateIdea(Idea idea) async {
    Logger.debug('FirestoreIdeasNotifier: Updating idea in Firestore: ${idea.id}');
    Logger.debug('FirestoreIdeasNotifier: Idea content: "${idea.content.substring(0, idea.content.length.clamp(0, 20))}${idea.content.length > 20 ? "..." : ""}"');

    try {
      // Update the idea in Firestore
      final result = await _firestoreService.updateIdea(_ideabookId, idea);
      Logger.debug('FirestoreIdeasNotifier: Updated idea in Firestore: ${idea.id}, result: $result');

      // No need to refresh the list as the Firestore listener will automatically update the state
      // when the document changes in Firestore

      return result;
    } catch (e) {
      Logger.error('FirestoreIdeasNotifier: Error updating idea in Firestore', e);
      rethrow;
    }
  }

  /// Delete an idea
  Future<bool> deleteIdea(String id) async {
    Logger.debug('Deleting idea from Firestore: $id');

    try {
      // Delete the idea from Firestore
      final result = await _firestoreService.deleteIdea(_ideabookId, id);
      Logger.debug('Deleted idea from Firestore: $id, result: $result');
      return result;
    } catch (e) {
      Logger.error('Error deleting idea from Firestore', e);
      rethrow;
    }
  }

  /// Set the flag to skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering ideas
  void skipNextUpdate() {
    Logger.debug('Setting flag to skip next Firestore update for ideabook $_ideabookId');
    _skipNextUpdate = true;
  }

  /// Update the sort order of an idea
  /// This is used when reordering ideas via drag-and-drop
  Future<bool> updateIdeaSortOrder(String ideaId, double newSortOrder) async {
    Logger.debug('FirestoreIdeasNotifier: Updating sort order for idea $ideaId to $newSortOrder');

    try {
      // First, get the current state of ideas
      final currentState = state;
      if (currentState is AsyncData<List<Idea>>) {
        // Find the idea in the current list
        final currentIdeas = currentState.value;
        final ideaIndex = currentIdeas.indexWhere((idea) => idea.id == ideaId);

        if (ideaIndex >= 0) {
          // Create a copy of the idea with the updated sort order
          final idea = currentIdeas[ideaIndex];
          final updatedIdea = idea.copyWith(sortOrder: newSortOrder);

          // Create a new list with the updated idea
          final updatedIdeas = List<Idea>.from(currentIdeas);
          updatedIdeas[ideaIndex] = updatedIdea;

          // Sort the list according to the new order
          updatedIdeas.sort((a, b) {
            final aValue = a.getEffectiveSortValue();
            final bValue = b.getEffectiveSortValue();
            return bValue.compareTo(aValue); // Descending order
          });

          // Update the state immediately with the new order
          state = AsyncData(updatedIdeas);

          // Skip the next update from Firestore since we've already updated the UI
          skipNextUpdate();

          // Now update Firestore
          final result = await _firestoreService.updateIdeaSortOrder(_ideabookId, ideaId, newSortOrder);
          Logger.debug('FirestoreIdeasNotifier: Updated sort order for idea $ideaId, result: $result');

          return result;
        }
      }

      // If we couldn't update the state optimistically, just update Firestore
      final result = await _firestoreService.updateIdeaSortOrder(_ideabookId, ideaId, newSortOrder);
      Logger.debug('FirestoreIdeasNotifier: Updated sort order for idea $ideaId, result: $result');

      return result;
    } catch (e) {
      Logger.error('FirestoreIdeasNotifier: Error updating sort order for idea $ideaId', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore ideas notifier
final firestoreIdeasNotifierProvider = StateNotifierProvider.family<FirestoreIdeasNotifier, AsyncValue<List<Idea>>, String>((ref, ideabookId) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return FirestoreIdeasNotifier(firestoreService, ideabookId);
});
