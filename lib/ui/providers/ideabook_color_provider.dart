import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/ideabook_provider.dart';
import 'package:voji/ui/providers/firestore_listener_provider.dart';
import 'package:voji/ui/providers/ideabook_swipe_provider.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Provider to track which ideabook is currently in color picking mode
final colorPickingIdeabookIdProvider = StateProvider<String?>((ref) => null);

/// Provider to update an ideabook's color
final updateIdeabookColorProvider = Provider<Function(String, IdeabookColor)>((ref) {
  return (String ideabookId, IdeabookColor newColor) async {
    // Log the color change for debugging
    Logger.debug('Updating ideabook $ideabookId color to ${newColor.name} (index: ${newColor.index})');

    // Make sure the Firestore listener is started
    final firestoreNotifier = ref.read(firestoreIdeabooksListenerNotifierProvider.notifier);

    // Check if we need to start listening
    final state = ref.read(firestoreIdeabooksListenerNotifierProvider);
    if (state is! AsyncData<List<Ideabook>>) {
      Logger.debug('Starting Firestore listener before updating color');
      firestoreNotifier.startListening();

      // Wait a moment for the listener to initialize
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // Get the ideabook using the listener pool
    final listenerPool = ref.read(firestoreListenerPoolProvider);
    final ideabook = await listenerPool.getIdeabookStream(ideabookId).first;

    if (ideabook == null) {
      Logger.error('Cannot update color: Ideabook $ideabookId not found in Firestore');
      return;
    }

    Logger.debug('Retrieved ideabook from Firestore: ${ideabook.id}, current color: ${ideabook.color.name}');

    // Create updated ideabook with new color
    final updatedIdeabook = ideabook.copyWith(
      color: newColor,
      updatedAt: DateTime.now(),
    );

    Logger.debug('Original ideabook: id=${ideabook.id}, color=${ideabook.color.name} (${ideabook.color.index})');
    Logger.debug('Updated ideabook: id=${updatedIdeabook.id}, color=${updatedIdeabook.color.name} (${updatedIdeabook.color.index})');

    // Force update directly using Firestore service
    // Create a direct update with just the color field
    final colorCode = IdUtils.encodeColor(newColor.index);
    Logger.debug('Directly updating Firestore document with color code: $colorCode');

    // Get Firebase instance
    final firestore = FirebaseFirestore.instance;
    final auth = FirebaseAuth.instance;
    final userId = auth.currentUser?.uid;

    if (userId == null) {
      Logger.error('Cannot update color: User is not authenticated');
      return;
    }

    // Create the document reference
    final docRef = firestore.collection('users').doc(userId).collection('ideabooks').doc(ideabookId);
    Logger.debug('Document path: ${docRef.path}');

    bool updateSuccess = false;

    try {
      // Update just the color field
      await docRef.update({'c': colorCode});
      Logger.debug('Successfully updated color field directly');
      updateSuccess = true;

      // Verify the update by using a listener
      final docSnapshot = await docRef.snapshots().first;
      if (docSnapshot.exists) {
        final data = docSnapshot.data();
        if (data != null && data.containsKey('c')) {
          final updatedColorCode = data['c'] as String;
          Logger.debug('Verified color update: "$updatedColorCode"');
        }
      }

      // Then use the regular update method to ensure all fields are updated
      final firestoreService = ref.read(firestoreServiceProvider);
      final result = await firestoreService.updateIdeabook(updatedIdeabook);
      Logger.debug('Regular update result: $result');
    } catch (e) {
      Logger.error('Error updating color directly', e);
      updateSuccess = false;
    }

    if (updateSuccess) {
      // Explicitly refresh the specific ideabook provider to update any UI watching it
      final _ = ref.refresh(ideabookProvider(ideabookId));
      Logger.debug('Refreshed ideabookProvider for $ideabookId after color update');
    }

    // Exit color picking mode and close left swipe
    ref.read(colorPickingIdeabookIdProvider.notifier).state = null;
    ref.read(leftSwipedIdeabookIdProvider.notifier).state = null;
  };
});
