import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/logger.dart';
import 'package:intl/intl.dart';

/// Widget for displaying an individual idea in the list
class IdeaItem extends ConsumerWidget {
  /// The idea to display
  final Idea idea;

  /// The ideabook ID this idea belongs to
  final String ideabookId;

  /// Constructor
  const IdeaItem({
    super.key,
    required this.idea,
    required this.ideabookId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Format the creation date
    final formattedDate = DateFormat('MMM d, yyyy').format(idea.createdAt);

    return Dismissible(
      key: Key('idea-${idea.id}'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      onDismissed: (direction) async {
        // Delete the idea using the notifier
        try {
          Logger.debug('Deleting idea with ID: ${idea.id}');
          Logger.debug('Using Firestore provider for idea deletion');

          // Delete the idea using Firestore service directly
          final firestoreService = ref.read(firestoreServiceProvider);
          final success = await firestoreService.deleteIdea(ideabookId, idea.id);

          if (success) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Idea deleted'),
                  duration: Duration(seconds: 1),
                ),
              );
            }
          } else {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Failed to delete idea'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        } catch (e) {
          Logger.error('Error deleting idea', e);
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error deleting idea: $e'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      },
      confirmDismiss: (direction) async {
        // Show confirmation dialog
        return await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Delete Idea'),
              content: const Text('Are you sure you want to delete this idea?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Delete'),
                ),
              ],
            );
          },
        );
      },
      child: InkWell(
        onTap: () {
          // This is a placeholder - no functionality yet
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Edit feature not implemented yet'),
              duration: Duration(seconds: 1),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Idea content
              Text(
                idea.content,
                style: VojiTheme.textStylesOf(context).bodyMedium,
              ),

              // Date
              const SizedBox(height: 8),
              Text(
                formattedDate,
                style: VojiTheme.textStylesOf(context).bodySmall,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
