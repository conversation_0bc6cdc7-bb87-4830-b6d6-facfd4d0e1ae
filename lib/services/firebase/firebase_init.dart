import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:voji/firebase_options.dart';
import 'package:voji/utils/logger.dart';

/// Enum to define Firebase environment modes
enum FirebaseEnvironment {
  /// Use production Firebase services
  production,

  /// Use Firebase emulators
  emulator
}

/// Current Firebase environment
/// Change this to switch between production and emulator mode
const FirebaseEnvironment firebaseEnvironment = FirebaseEnvironment.production;

/// Helper method to check if emulators should be used
bool get useEmulators =>
    firebaseEnvironment == FirebaseEnvironment.emulator && kDebugMode;

/// Initialize Firebase and optionally connect to emulators
Future<void> initializeFirebase() async {
  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    Logger.debug('Firebase initialized successfully');

    // Initialize Firebase App Check
    await _initializeAppCheck();

    // Log the current environment
    Logger.debug('Firebase environment: ${firebaseEnvironment.name}');

    // Connect to emulators if enabled
    if (useEmulators) {
      Logger.debug('Connecting to Firebase emulators');

      // Define emulator host and ports
      const String emulatorHost = '127.0.0.1';
      const int functionsPort = 5001;
      const int authPort = 9099;
      const int firestorePort = 8080; // Default Firestore emulator port

      // Connect to Functions emulator
      try {
        FirebaseFunctions.instance.useFunctionsEmulator(emulatorHost, functionsPort);
        Logger.debug('Connected to Functions emulator at $emulatorHost:$functionsPort');
      } catch (e) {
        Logger.error('Failed to connect to Functions emulator', e);
      }

      // Connect to Auth emulator
      try {
        FirebaseAuth.instance.useAuthEmulator(emulatorHost, authPort);
        Logger.debug('Connected to Auth emulator at $emulatorHost:$authPort');

        // In debug mode, we can pre-configure some test accounts
        if (kDebugMode) {
          Logger.debug('Auth emulator is ready for testing');
        }
      } catch (e) {
        Logger.error('Failed to connect to Auth emulator', e);
      }

      // Connect to Firestore emulator
      try {
        FirebaseFirestore.instance.useFirestoreEmulator(emulatorHost, firestorePort);
        Logger.debug('Connected to Firestore emulator at $emulatorHost:$firestorePort');
      } catch (e) {
        Logger.error('Failed to connect to Firestore emulator', e);
      }

      Logger.debug('Firebase emulators connected');
    } else {
      Logger.debug('Using production Firebase services');
    }

    // Enable offline persistence for Firestore
    try {
      FirebaseFirestore.instance.settings = Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
      Logger.debug('Firestore offline persistence enabled successfully');
    } catch (e) {
      Logger.error('Failed to enable Firestore offline persistence', e);
      // Don't rethrow as this is not critical for app functionality
    }
  } catch (e) {
    Logger.error('Error initializing Firebase', e);
    rethrow;
  }
}

/// Initialize Firebase App Check
///
/// App Check helps protect your backend resources from abuse by providing an
/// attestation that the request is coming from your authentic app.
///
/// Before using this in production:
/// 1. Register your apps in Firebase Console > Project Settings > App Check
/// 2. For web, create a reCAPTCHA v3 site key and replace the placeholder below
/// 3. For iOS, ensure you've configured your app for App Attest/DeviceCheck
/// 4. For Android, ensure you've configured your app for Play Integrity
///
/// After testing that App Check works correctly, enable enforcement in the Firebase Console
/// for each service you want to protect (Firestore, Storage, Functions, etc.)
///
/// For local development testing:
/// 1. The debug provider will print a debug token to the console when first used
/// 2. Register this token in Firebase Console > App Check > Manage debug tokens
/// 3. This allows your app to access Firebase services during development
Future<void> _initializeAppCheck() async {
  try {
    // Get the reCAPTCHA site key for web
    // TODO: Replace with your actual reCAPTCHA v3 site key from Firebase console
    // You can get this from Firebase Console > Project Settings > App Check
    const String recaptchaSiteKey = '6Lc_REPLACE_THIS_WITH_YOUR_ACTUAL_SITE_KEY';

    // Log whether we're using debug providers
    if (kDebugMode) {
      Logger.debug('Initializing App Check with debug providers for development');
    }

    // Initialize App Check with appropriate providers for each platform
    await FirebaseAppCheck.instance.activate(
      // For web, use reCAPTCHA v3
      webProvider: ReCaptchaV3Provider(recaptchaSiteKey),

      // For Android, use Play Integrity in production, Debug provider in development
      // The Debug provider should only be used during app development
      androidProvider: kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,

      // For iOS, use App Attest with fallback to Device Check
      // Debug provider in development
      // App Attest is available on iOS 14.0+ and provides stronger attestation
      appleProvider: kDebugMode ? AppleProvider.debug : AppleProvider.appAttest,
    );

    // Enable token auto refresh to prevent token expiration issues
    // This determines when tokens are refreshed based on their TTL (default is 0.5)
    // Setting this to true ensures tokens are automatically refreshed before expiration
    FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
    Logger.debug('App Check token auto-refresh enabled');

    if (kDebugMode) {
      Logger.debug('Firebase App Check initialized with debug providers');
      Logger.debug('If this is your first time running the app, look for a debug token in the logs');
      Logger.debug('Register this token in Firebase Console > App Check > Manage debug tokens');
    } else {
      Logger.debug('Firebase App Check initialized with production providers');
    }
  } catch (e) {
    Logger.error('Failed to initialize Firebase App Check', e);
    // Don't rethrow as this is not critical for app functionality
  }
}

/// Force refresh the App Check token
///
/// This is useful when encountering App Check token invalid errors,
/// especially during authentication state changes (sign-out/sign-in cycles)
Future<void> refreshAppCheckToken() async {
  try {
    Logger.debug('Attempting to refresh App Check token');

    // Get a new token from App Check
    final token = await FirebaseAppCheck.instance.getToken(true); // Force refresh

    if (token != null) {
      Logger.debug('App Check token refreshed successfully');
    } else {
      Logger.debug('App Check token refresh returned null');
    }
  } catch (e) {
    Logger.error('Failed to refresh App Check token', e);
    // Don't rethrow as this is not critical for app functionality
  }
}

/// Check if an error is related to App Check token issues
bool isAppCheckTokenError(Object error) {
  final errorString = error.toString().toLowerCase();
  return errorString.contains('app check token is invalid') ||
         errorString.contains('app check') ||
         errorString.contains('firebase_auth/internal-error');
}